<template>
  <div>
    <component :is="rulesComponent">
      <template slot="operater">
        <div class="switch-custom-rules-operater">
          <div
            class="tab-button"
            :class="{ active: rulesComponent === 'CustomRules' }"
            @click="rulesComponent = 'CustomRules'">
            系统规则
          </div>
          <div
            class="tab-button"
            :class="{ active: rulesComponent === 'ModelRules' }"
            @click="rulesComponent = 'ModelRules'">
            大模型规则
          </div>
        </div>
      </template>
    </component>
  </div>
</template>
<script>
import CustomRules from './CustomRules.vue';
import ModelRules from '@/custom/general/pages/ModelRules.vue';

export default {
  name: 'switch-custom-rules',
  components: {
    CustomRules,
    ModelRules,
  },

  data() {
    return {
      rulesComponent: 'CustomRules',
    };
  },
};
</script>
<style lang="scss" scoped>
.switch-custom-rules-operater {
  display: flex;
  align-items: center;
  margin-right: 20px;
  .tab-button {
    padding: 10px 20px;
    border: 1px solid #9a9a9a;
    cursor: pointer;
    &:first-child {
      border-radius: 6px 0 0 6px;
      border-right: none;
    }
    &:last-child {
      border-radius: 0 6px 6px 0;
    }
    &.active {
      color: #316ec5;
      border: 1px solid #316ec5;
    }
  }
}
</style>
