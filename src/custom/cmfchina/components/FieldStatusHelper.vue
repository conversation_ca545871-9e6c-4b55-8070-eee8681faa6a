<template>
  <el-tooltip
    effect="light"
    placement="bottom-end"
    popper-class="audit-status-info-tooltip">
    <div slot="content" class="content">
      <p v-for="item in statusList" :key="item.value">
        <label>{{ item.label }}:</label>
        <span :class="['color', item.colorValue]">
          {{ item.colorLabel }}
        </span>
      </p>
    </div>
    <el-button
      type="text"
      size="mini"
      icon="el-icon-question"
      class="button-info">
    </el-button>
  </el-tooltip>
</template>

<script>
export default {
  name: 'field-status-helper',
  props: {
    options: {
      type: Object,
      required: true,
    },
  },
  computed: {
    statusList() {
      const result = Object.values(
        Object.keys(this.options).reduce((acc, key) => {
          const item = this.options[key];
          if (!acc[item.colorValue]) {
            acc[item.colorValue] = { ...item };
          } else {
            acc[item.colorValue].label += `&${item.label}`;
          }
          return acc;
        }, {}),
      );

      const order = ['orange', 'red', 'blue', 'black'];
      const orderedResult = result.sort((a, b) => {
        return order.indexOf(a.colorValue) - order.indexOf(b.colorValue);
      });

      return orderedResult;
    },
  },
};
</script>
